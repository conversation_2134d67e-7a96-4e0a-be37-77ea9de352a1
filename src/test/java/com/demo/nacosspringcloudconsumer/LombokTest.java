package com.demo.nacosspringcloudconsumer;

import com.demo.nacosspringcloudconsumer.entity.Student;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试Lombok注解是否正常工作
 */
public class LombokTest {

    @Test
    public void testLombokGettersAndSetters() {
        // 测试无参构造器
        Student student = new Student();
        
        // 测试setter方法
        student.setId(1);
        student.setName("张三");
        student.setAge(20);
        
        // 测试getter方法
        assertEquals(Integer.valueOf(1), student.getId());
        assertEquals("张三", student.getName());
        assertEquals(Integer.valueOf(20), student.getAge());
        
        // 测试全参构造器
        Student student2 = new Student(2, "李四", 25);
        assertEquals(Integer.valueOf(2), student2.getId());
        assertEquals("李四", student2.getName());
        assertEquals(Integer.valueOf(25), student2.getAge());
        
        // 测试toString方法
        String toString = student.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("1"));
        assertTrue(toString.contains("张三"));
        assertTrue(toString.contains("20"));
        
        System.out.println("Lombok测试通过！");
        System.out.println("Student toString: " + student.toString());
    }
}
