package com.demo.nacosspringcloudconsumer.feignService;

import com.demo.nacosspringcloudconsumer.entity.Student;
import com.demo.nacosspringcloudconsumer.fallbackService.ProviderFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Name ProviderFeignService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/8 15:46
 */
@FeignClient(name = "provider01", path = "/provider", fallbackFactory = ProviderFeignFallbackFactory.class)
public interface ProviderFeignService {

    @GetMapping("/test")
    public String test();

    @GetMapping("/testParam")
    public String testParam(@RequestParam("param") String param);

    @PostMapping("/getStudent")
    public Student getStudent(@RequestBody Student student);
}
