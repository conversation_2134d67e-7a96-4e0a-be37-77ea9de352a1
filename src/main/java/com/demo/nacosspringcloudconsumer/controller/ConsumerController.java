package com.demo.nacosspringcloudconsumer.controller;

import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.demo.nacosspringcloudconsumer.entity.Student;
import com.demo.nacosspringcloudconsumer.feignService.ProviderFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

/**
 * @Name ConsumerController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/6 10:57
 */
@RestController
public class ConsumerController {
//    @Autowired
//    private RestTemplate restTemplate;
    @Autowired
    private ProviderFeignService providerFeignService;

    @GetMapping("/test")
    public String test() {
//        String result = restTemplate.getForObject("http://provider01/provider/test", String.class);
        String result = providerFeignService.test();
        System.out.println(result);
        return result;
    }

    @GetMapping("/testParam")
    public String testParam(String testParam) {
//        String result = restTemplate.getForObject("http://provider01/provider/testParam?param=" + testParam, String.class);
        String result = providerFeignService.testParam(testParam);
        System.out.println(result);
        return result;
    }

    @PostMapping("/getStudent")
    public String getStudent(@RequestBody Student student) {
//        Student stu = restTemplate.postForObject("http://provider01/provider/getStudent", student, Student.class);
        Student stu = providerFeignService.getStudent(student);
        return stu == null ? "null" : stu.toString();
    }
}
