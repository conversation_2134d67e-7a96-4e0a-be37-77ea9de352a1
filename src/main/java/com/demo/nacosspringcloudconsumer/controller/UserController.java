package com.demo.nacosspringcloudconsumer.controller;

import com.demo.nacosspringcloudconsumer.entity.User;
import com.demo.nacosspringcloudconsumer.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @Name TestController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/9/15 16:30
 */
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserService userService;

    @GetMapping("/getUser")
    public User getUser(Integer code) {
        List<User> userList = new ArrayList<>();
        userList.add(new User(1, "张三", 18));
        userList.add(new User(2, "李四", 19));
        userList.add(new User(3, "王五", 20));
        userList.add(new User(4, "赵六", 21));
        userList.add(new User(5, "钱七", 22));

        return userList.stream().filter(x -> x.getCode().equals(code)).findFirst().orElse(null);
    }

    @GetMapping("/query")
    public String query() {
        System.out.println("查询用户");
        userService.queryRoles();
        return "查询用户成功";
    }

    @GetMapping("/update")
    public String update(User user) {
        return "更新用户成功";
    }

    @GetMapping("/save")
    public String save(){
        System.out.println("保存用户");
        userService.queryRoles();
        return "保存用户成功";
    }
}
