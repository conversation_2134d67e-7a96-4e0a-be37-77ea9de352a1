package com.demo.nacosspringcloudconsumer.fallbackService;

import com.demo.nacosspringcloudconsumer.entity.Student;
import com.demo.nacosspringcloudconsumer.feignService.ProviderFeignService;
import feign.hystrix.FallbackFactory;

/**
 * @Name ProviderFeignFallbackService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/9/12 11:15
 */
public class ProviderFeignFallbackFactory implements FallbackFactory<ProviderFeignService> {
    @Override
    public ProviderFeignService create(Throwable throwable) {
        return new ProviderFeignService(){

            @Override
            public String test() {
                return "";
            }

            @Override
            public String testParam(String param) {
                return "";
            }

            @Override
            public Student getStudent(Student student) {
                return null;
            }
        };
    }
}
